<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * Model for the 'users' table.
 * Represents users that belong to organizations with business identifiers, profile, security, and audit fields.
 */
class UserModel extends Model
{
    /**
     * @var string Database connection group.
     */
    protected $DBGroup = 'default';

    /**
     * @var string The database table this model represents.
     */
    protected $table = 'users';

    /**
     * @var string The primary key for the table.
     */
    protected $primaryKey = 'id';

    /**
     * @var bool Whether the model should use auto-incrementing primary keys.
     */
    protected $useAutoIncrement = true;

    /**
     * @var string The type of result to return. 'array' or 'object'.
     */
    protected $returnType = 'array';

    /**
     * @var bool Whether the model should use soft deletes (deleted_at column).
     */
    protected $useSoftDeletes = true;

    /**
     * @var bool Whether to protect fields from mass assignment.
     */
    protected $protectFields = true;

    /**
     * @var array The fields that can be mass-assigned.
     */
    protected $allowedFields = [
        // Business identifiers
        'organization_id', 'user_code', 'username', 'email',
        // Profile
        'name', 'role', 'profile_photo_path',
        // Security & lifecycle
        'password_hash', 'activation_token', 'is_activated', 'password_reset_token', 'last_login_at',
        // Audit-by columns
        'created_by', 'updated_by', 'deleted_by',
    ];

    /**
     * @var bool Whether the model should use timestamps.
     */
    protected $useTimestamps = true;

    /**
     * @var string The name of the `created_at` column.
     */
    protected $createdField = 'created_at';

    /**
     * @var string The name of the `updated_at` column.
     */
    protected $updatedField = 'updated_at';

    /**
     * @var string The name of the `deleted_at` column.
     */
    protected $deletedField = 'deleted_at';

    /**
     * @var array Validation rules for the model's fields.
     */
    protected $validationRules = [
        'organization_id' => 'required|numeric',
        'user_code'       => 'permit_empty|is_unique[users.user_code]',
        'username'        => 'required|is_unique[users.username]',
        'email'           => 'required|valid_email|is_unique[users.email]',
        'name'            => 'required',
        'role'            => 'required',
        'password_hash'   => 'required',
    ];

    /**
     * @var array Custom error messages for validation rules.
     */
    protected $validationMessages = [
        'organization_id' => [
            'required' => 'Organization ID is required.',
            'numeric'  => 'Organization ID must be a number.',
        ],
        'user_code' => [
            'is_unique'  => 'This user code already exists.',
        ],
        'username' => [
            'required'   => 'Username is required.',
            'is_unique'  => 'This username already exists.',
        ],
        'email' => [
            'required'     => 'Email is required.',
            'valid_email'  => 'Please enter a valid email address.',
            'is_unique'    => 'This email address already exists.',
        ],
        'name' => [
            'required' => 'Name is required.',
        ],
        'role' => [
            'required' => 'Role is required.',
        ],
        'password_hash' => [
            'required' => 'Password is required.',
        ],
    ];

    /**
     * @var bool Whether to skip validation on insert/update.
     */
    protected $skipValidation = false;

    /**
     * @var array Callbacks for before/after insert/update/delete operations.
     */
    protected $beforeInsert = ['generateUserCode'];
    protected $beforeUpdate = [];

    /**
     * Generate unique user code before insert
     *
     * @param array $data
     * @return array
     */
    protected function generateUserCode(array $data): array
    {
        if (!isset($data['data']['user_code']) || empty($data['data']['user_code'])) {
            $data['data']['user_code'] = $this->createUniqueUserCode();
        }
        return $data;
    }

    /**
     * Create a unique alphanumeric user code (8-12 characters)
     *
     * @return string
     */
    public function createUniqueUserCode(): string
    {
        $maxAttempts = 100;
        $attempt = 0;

        do {
            $code = $this->generateRandomCode();
            $attempt++;
        } while ($this->where('user_code', $code)->countAllResults() > 0 && $attempt < $maxAttempts);

        if ($attempt >= $maxAttempts) {
            throw new \RuntimeException('Unable to generate unique user code after ' . $maxAttempts . ' attempts');
        }

        return $code;
    }

    /**
     * Generate random alphanumeric code
     *
     * @return string
     */
    private function generateRandomCode(): string
    {
        $length = rand(8, 12);
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $code = '';

        for ($i = 0; $i < $length; $i++) {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $code;
    }

    /**
     * Get users for a specific organization
     *
     * @param int $organizationId
     * @param array $filters
     * @return array
     */
    public function getUsersByOrganization(int $organizationId, array $filters = []): array
    {
        $builder = $this->where('organization_id', $organizationId);

        // Apply filters
        if (!empty($filters['role'])) {
            $builder->where('role', $filters['role']);
        }

        if (!empty($filters['is_activated'])) {
            $builder->where('is_activated', $filters['is_activated']);
        }

        if (!empty($filters['search'])) {
            $builder->groupStart()
                   ->like('name', $filters['search'])
                   ->orLike('email', $filters['search'])
                   ->orLike('username', $filters['search'])
                   ->orLike('user_code', $filters['search'])
                   ->groupEnd();
        }

        // Apply sorting
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'DESC';
        $builder->orderBy($sortField, $sortDirection);

        return $builder->findAll();
    }

    /**
     * Get user with organization details
     *
     * @param int $userId
     * @return array|null
     */
    public function getUserWithOrganization(int $userId): ?array
    {
        return $this->select('users.*, organizations.name as org_name, organizations.org_code')
                   ->join('organizations', 'organizations.id = users.organization_id', 'left')
                   ->where('users.id', $userId)
                   ->first();
    }

    /**
     * Check if user code exists
     *
     * @param string $code
     * @return bool
     */
    public function userCodeExists(string $code): bool
    {
        return $this->where('user_code', $code)->countAllResults() > 0;
    }

    /**
     * Get user by email or username
     *
     * @param string $identifier
     * @return array|null
     */
    public function getUserByIdentifier(string $identifier): ?array
    {
        return $this->groupStart()
                   ->where('email', $identifier)
                   ->orWhere('username', $identifier)
                   ->groupEnd()
                   ->first();
    }

    /**
     * Update user activation status
     *
     * @param int $userId
     * @param bool $isActivated
     * @param int $updatedBy
     * @return bool
     */
    public function updateUserActivation(int $userId, bool $isActivated, int $updatedBy): bool
    {
        return $this->update($userId, [
            'is_activated' => $isActivated ? 1 : 0,
            'updated_by' => $updatedBy
        ]);
    }

    /**
     * Get user statistics for organization
     *
     * @param int $organizationId
     * @return array
     */
    public function getUserStats(int $organizationId): array
    {
        $total = $this->where('organization_id', $organizationId)->countAllResults();
        $activated = $this->where('organization_id', $organizationId)->where('is_activated', 1)->countAllResults();
        $pending = $this->where('organization_id', $organizationId)->where('is_activated', 0)->countAllResults();
        
        // Count by roles
        $admins = $this->where('organization_id', $organizationId)->where('role', 'admin')->countAllResults();
        $moderators = $this->where('organization_id', $organizationId)->where('role', 'moderator')->countAllResults();
        $editors = $this->where('organization_id', $organizationId)->where('role', 'editor')->countAllResults();
        $users = $this->where('organization_id', $organizationId)->where('role', 'user')->countAllResults();

        return [
            'total' => $total,
            'activated' => $activated,
            'pending' => $pending,
            'roles' => [
                'admin' => $admins,
                'moderator' => $moderators,
                'editor' => $editors,
                'user' => $users
            ]
        ];
    }
}
